# Easy WhatsApp Orders - Quick Setup Guide

## Installation

### Method 1: WordPress Admin (Recommended)

1. Go to **Plugins > Add New** in WordPress admin
2. Click **Upload Plugin**
3. Choose the plugin zip file
4. Click **Install Now**
5. Click **Activate Plugin**

### Method 2: FTP Upload

1. Extract the plugin zip file
2. Upload the folder to `/wp-content/plugins/`
3. Go to **Plugins** in WordPress admin
4. Find "Easy WhatsApp Orders" and click **Activate**

### Requirements

- WordPress 5.0+
- WooCommerce 3.0+
- PHP 7.4+

## Quick Start

### 1. Basic Setup

- Go to **Easy WhatsApp Orders > Settings**
- Enter your WhatsApp number: `+************`
- Choose where to show buttons: Product pages ✓ Shop pages ✓
- Click **Save Changes**

### 2. Create Order Form

- Go to **Form Builder**
- Drag fields: Name, Email, Phone
- Click **Save Form**
- Go back to **Settings** and select your form

### 3. View Orders

- Go to **Orders** to see customer requests
- Update order status: Pending → Processing → Completed

### 4. Advanced Rules (Optional)

- Go to **Rules Engine**
- Set which categories show buttons
- Set business hours
- Configure user permissions

## That's it! Your WhatsApp ordering system is ready.
